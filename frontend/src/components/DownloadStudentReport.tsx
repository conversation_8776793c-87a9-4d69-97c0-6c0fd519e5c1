import React, { useState } from 'react';
import { PDFDocument, rgb } from 'pdf-lib';
import { ViewColumnsIcon } from '@heroicons/react/24/outline';
import { useS3Utils } from '@/hooks/useS3Utils';
import { markdownToPlainText } from '@/utils/markdownToPdf';
import { BoundingBox } from '@/types/aegisGrader';

// Type definitions - Updated for new UPSC evaluation structure
interface CriterionBreakdown {
  criterion: string;
  score: string;
  maxScore?: string;
}

interface Paragraph {
  text: string;
  boundingBox: BoundingBox;
}

interface FeedbackSection {
  title: string;
  content: string;
  subsections?: { 
    title: string; 
    content: string; 
    bounding_box?: BoundingBox;
    subsections?: { title: string; content: string; bounding_box?: BoundingBox }[];
  }[];
}

interface QuestionBreakdown {
  questionNumber: string;
  marksAwarded: number;
  marksPossible: number;
  percentage: number;
  feedback: string;
  structuredFeedback?: FeedbackSection[];
  criteriaBreakdown: CriterionBreakdown[];
  paragraphs?: Paragraph[];
}

interface EvaluationBreakdown {
  totalMarks: number;
  maxMarks: number;
  overallPercentage: number;
  questions: QuestionBreakdown[];
}

interface SubmissionData {
  studentName: string;
  rollNumber: string;
  totalMarks: number;
  maxMarks: number;
  percentage: number;
  pdfUrl?: string;
  detailedBreakdown: EvaluationBreakdown;
  subjectName?: string;
}

interface DownloadStudentReportProps {
  submissionData: SubmissionData;
  breakdownContentRef: React.RefObject<HTMLDivElement>;
}

const DownloadStudentReport: React.FC<DownloadStudentReportProps> = ({ submissionData, breakdownContentRef }) => {
  const [isDownloadingSideBySide, setIsDownloadingSideBySide] = useState(false);
  const { fetchPdfAsBlob } = useS3Utils();



  // Theme colors for PDF (light, professional colors)
  const themeColors = {
    primary: rgb(0.08, 0.12, 0.32), // Deep blue from your theme
    secondary: rgb(0.2, 0.3, 0.5), // Medium blue
    accent: rgb(0.0, 0.5, 1.0), // Bright blue accent
    muted: rgb(0.4, 0.4, 0.5), // Muted text
    lightMuted: rgb(0.6, 0.6, 0.7), // Light muted text
    success: rgb(0.2, 0.6, 0.3), // Green for good scores
    warning: rgb(0.8, 0.6, 0.2), // Orange for average scores
    danger: rgb(0.8, 0.3, 0.3), // Red for poor scores
    background: rgb(1, 1, 1), // White background
    border: rgb(0.9, 0.9, 0.95), // Light border
  };

  // Optimized text drawing configuration for 3-column layout
  const textConfig = {
    fontSize: {
      header: 12,
      subheader: 10,
      body: 8,
      small: 7,
      tiny: 6
    },
    lineHeight: {
      header: 16,
      subheader: 14,
      body: 11,
      small: 10,
      tiny: 8
    },
    spacing: {
      section: 12,
      paragraph: 8,
      line: 3
    }
  };

  const downloadSideBySideReport = async () => {
    console.log('Downloading side-by-side report...');
    setIsDownloadingSideBySide(true);

    try {
      if (submissionData.pdfUrl) {
        await createAnnotatedAnswerSheet();
      } else {
        alert('No answer sheet PDF available for side-by-side view.');
      }
      console.log('Side-by-side report downloaded successfully');
    } catch (error) {
      console.error('Error generating side-by-side report:', error);
      alert('Failed to generate side-by-side report. Please try again.');
    } finally {
      setIsDownloadingSideBySide(false);
    }
  };

  const createAnnotatedAnswerSheet = async () => {
    try {
      console.log('Creating side-by-side answer sheet...');
      
      if (!submissionData.pdfUrl) {
        console.log('No student answer PDF URL available');
        return;
      }

      const evaluationData = submissionData.detailedBreakdown;
      console.log('Evaluation data:', evaluationData);
      
      if (!evaluationData || !evaluationData.questions) {
        console.log('No evaluation data available for annotations');
        return;
      }

      console.log('Fetching student answer PDF from S3:', submissionData.pdfUrl);
      const pdfBlob = await fetchPdfAsBlob(submissionData.pdfUrl);
      console.log('PDF blob received, size:', pdfBlob.size);
      
      const pdfBytes = await pdfBlob.arrayBuffer();
      const pdfHeader = new Uint8Array(pdfBytes.slice(0, 4));
      const pdfSignature = new TextDecoder().decode(pdfHeader);
      
      if (pdfSignature !== '%PDF') {
        throw new Error(`Invalid PDF file. File header: ${pdfSignature}. Expected: %PDF`);
      }
      
      const originalPdfDoc = await PDFDocument.load(pdfBytes);
      const originalPages = originalPdfDoc.getPages();
      
      const newPdfDoc = await PDFDocument.create();
      
      // Try to embed logo
      let logoImage = null;
      try {
        const logoResponse = await fetch('/logo_accent.png');
        if (logoResponse.ok) {
          const logoArrayBuffer = await logoResponse.arrayBuffer();
          logoImage = await newPdfDoc.embedPng(logoArrayBuffer);
        }
      } catch (logoError) {
        console.log('Could not load logo, proceeding without it:', logoError);
      }

      // Process each page of the original PDF
      for (let pageIndex = 0; pageIndex < originalPages.length; pageIndex++) {
        const originalPage = originalPages[pageIndex];
        const pageNumber = pageIndex + 1;

        // Create a new page with landscape orientation for 3-column layout
        const newPage = newPdfDoc.addPage([1190.56, 841.89]); // A4 landscape
        const { width: pageWidth, height: pageHeight } = newPage.getSize();
        const margin = 30; // Reduced margin for more content space

        // Define 3-column layout
        const columnWidth = (pageWidth - 4 * margin) / 3; // 3 equal columns with margins
        const column1X = margin; // Original PDF column
        const column2X = margin + columnWidth + margin; // Analysis column
        const column3X = margin + 2 * (columnWidth + margin); // Recommendations column
        
        // Optimized text drawing function for 3-column layout
        const drawText = (
          text: string,
          x: number,
          y: number,
          maxWidth: number,
          fontSize: number,
          page: any,
          color = themeColors.primary,
          options: {
            isBold?: boolean,
            isTitle?: boolean,
            maxLines?: number
          } = {}
        ): number => {
          if (!text || typeof text !== 'string') return y;

          // Clean and prepare text
          const cleanText = markdownToPlainText(text);
          if (!cleanText) return y;

          // Calculate dimensions with tighter spacing
          const charWidth = fontSize * 0.5; // Reduced character width for better fit
          const lineHeight = textConfig.lineHeight[fontSize === textConfig.fontSize.header ? 'header' :
                                                   fontSize === textConfig.fontSize.subheader ? 'subheader' :
                                                   fontSize === textConfig.fontSize.body ? 'body' :
                                                   fontSize === textConfig.fontSize.small ? 'small' : 'tiny'];

          // Ensure we don't exceed boundaries
          const safeMaxWidth = Math.min(maxWidth, columnWidth - 5); // Use column width instead of page width
          const maxCharsPerLine = Math.floor(safeMaxWidth / charWidth);

          // Split text into lines with better word wrapping
          const words = cleanText.split(' ');
          const lines: string[] = [];
          let currentLine = '';

          for (const word of words) {
            const testLine = currentLine + (currentLine ? ' ' : '') + word;
            if (testLine.length <= maxCharsPerLine) {
              currentLine = testLine;
            } else {
              if (currentLine) {
                lines.push(currentLine);
                currentLine = word;
              } else {
                // Word is too long, truncate it
                lines.push(word.substring(0, maxCharsPerLine - 3) + '...');
                currentLine = '';
              }
            }
          }
          if (currentLine) {
            lines.push(currentLine);
          }

          // Apply max lines limit if specified
          const finalLines = options.maxLines && options.maxLines > 0 ? lines.slice(0, options.maxLines) : lines;
          if (options.maxLines && options.maxLines > 0 && lines.length > options.maxLines) {
            finalLines[finalLines.length - 1] = finalLines[finalLines.length - 1].substring(0, maxCharsPerLine - 3) + '...';
          }

          // Draw lines with tighter spacing
          let currentY = y;
          finalLines.forEach((line) => {
            page.drawText(line, {
              x,
              y: currentY,
              size: fontSize,
              color
            });
            currentY -= lineHeight;
          });

          return currentY - (options.isTitle ? textConfig.spacing.section : textConfig.spacing.paragraph);
        };
        
        // Compact header for 3-column layout
        const headerY = pageHeight - 40; // Reduced header height
        if (logoImage) {
          // Draw smaller logo
          const logoWidth = 20;
          const logoHeight = 20;
          newPage.drawImage(logoImage, {
            x: margin,
            y: headerY - logoHeight + 5,
            width: logoWidth,
            height: logoHeight,
          });
          // Draw company name next to logo
          newPage.drawText('AegisScholar', {
            x: margin + logoWidth + 10,
            y: headerY,
            size: textConfig.fontSize.subheader,
            color: themeColors.primary
          });
        } else {
          newPage.drawText('AegisScholar', {
            x: margin,
            y: headerY,
            size: textConfig.fontSize.subheader,
            color: themeColors.primary
          });
        }

        // Add subtitle inline with header
        newPage.drawText('- Annotated Answer Sheet', {
          x: logoImage ? margin + 20 + 10 + 100 : margin + 100,
          y: headerY,
          size: textConfig.fontSize.body,
          color: themeColors.secondary
        });

        // Add student info in top right
        const studentInfoX = pageWidth - 300; // Fixed position from right
        const studentInfoText = `${submissionData.studentName} | ${submissionData.rollNumber}`;
        newPage.drawText(studentInfoText.length > 40 ? studentInfoText.substring(0, 37) + '...' : studentInfoText, {
          x: studentInfoX,
          y: headerY,
          size: textConfig.fontSize.body,
          color: themeColors.muted
        });

        // Add score below student info
        const scoreText = `Score: ${submissionData.totalMarks}/${submissionData.maxMarks} (${submissionData.percentage}%)`;
        newPage.drawText(scoreText, {
          x: studentInfoX,
          y: headerY - 15,
          size: textConfig.fontSize.body,
          color: submissionData.percentage >= 80 ? themeColors.success :
                 submissionData.percentage >= 60 ? themeColors.warning : themeColors.danger
        });
        
        // Embed the original page in the first column
        const embeddedPage = await newPdfDoc.embedPage(originalPage);
        const scale = Math.min(columnWidth / embeddedPage.width, (pageHeight - 120) / embeddedPage.height);
        const embeddedWidth = embeddedPage.width * scale;
        const embeddedHeight = embeddedPage.height * scale;

        // Position embedded page in first column
        const embeddedX = column1X;
        const embeddedY = pageHeight - embeddedHeight - 70; // Reduced top margin

        newPage.drawPage(embeddedPage, {
          x: embeddedX,
          y: embeddedY,
          width: embeddedWidth,
          height: embeddedHeight
        });

        // Add page number label
        newPage.drawText(`Page ${pageNumber}`, {
          x: embeddedX,
          y: embeddedY - 12,
          size: textConfig.fontSize.small,
          color: themeColors.muted
        });

        // Add column separators for visual clarity
        const separatorStartY = pageHeight - 60;
        const separatorEndY = 40;

        // Separator between column 1 and 2
        newPage.drawLine({
          start: { x: column1X + columnWidth + margin/2, y: separatorStartY },
          end: { x: column1X + columnWidth + margin/2, y: separatorEndY },
          thickness: 0.5,
          color: themeColors.border
        });

        // Separator between column 2 and 3
        newPage.drawLine({
          start: { x: column2X + columnWidth + margin/2, y: separatorStartY },
          end: { x: column2X + columnWidth + margin/2, y: separatorEndY },
          thickness: 0.5,
          color: themeColors.border
        });
        
        // Helper function to extract structured content from feedback
        const extractStructuredContent = (content: string) => {
          const sections = {
            coreClaim: '',
            evidence: '',
            reasoning: '',
            linkToQuestion: '',
            analysis: '',
            specificImprovements: '',
            evidenceToAdd: '',
            structuralFix: ''
          };

          // Extract Core Claim
          const coreClaimMatch = content.match(/\*\*Core Claim:\*\*\s*(.*?)(?=\*\*|$)/);
          if (coreClaimMatch) sections.coreClaim = coreClaimMatch[1].trim();

          // Extract Evidence
          const evidenceMatch = content.match(/\*\*Evidence:\*\*\s*(.*?)(?=\*\*|$)/);
          if (evidenceMatch) sections.evidence = evidenceMatch[1].trim();

          // Extract Reasoning
          const reasoningMatch = content.match(/\*\*Reasoning:\*\*\s*(.*?)(?=\*\*|$)/);
          if (reasoningMatch) sections.reasoning = reasoningMatch[1].trim();

          // Extract Link to Question
          const linkMatch = content.match(/\*\*Link to Question:\*\*\s*(.*?)(?=\*\*|$)/);
          if (linkMatch) sections.linkToQuestion = linkMatch[1].trim();

          // Extract Analysis
          const analysisMatch = content.match(/\*\*Analysis:\*\*\s*(.*?)(?=\*\*|$)/);
          if (analysisMatch) sections.analysis = analysisMatch[1].trim();

          // Extract specific improvements, evidence to add, structural fix
          const improvementsMatch = content.match(/specific_improvements['":\s]*\[(.*?)\]/);
          if (improvementsMatch) sections.specificImprovements = improvementsMatch[1].trim();

          const evidenceToAddMatch = content.match(/evidence_to_add['":\s]*['"](.*?)['"]/);
          if (evidenceToAddMatch) sections.evidenceToAdd = evidenceToAddMatch[1].trim();

          const structuralFixMatch = content.match(/structural_fix['":\s]*['"](.*?)['"]/);
          if (structuralFixMatch) sections.structuralFix = structuralFixMatch[1].trim();

          return sections;
        };

        // Implement 3-column layout for feedback
        const contentStartY = pageHeight - 70; // Start below header

        // Find questions that have feedback for this page
        const questionsForThisPage = evaluationData.questions.filter((question: QuestionBreakdown) => {
          if (question.structuredFeedback && question.structuredFeedback.length > 0) {
            return question.structuredFeedback.some(section =>
              section.subsections && section.subsections.some(subsection =>
                subsection.bounding_box &&
                subsection.bounding_box.page === pageNumber
              )
            );
          }
          return false;
        });

        // Render feedback in 3-column layout
        if (questionsForThisPage.length > 0) {
          // Column 2: Analysis content
          let analysisY = contentStartY;
          newPage.drawText('Analysis', {
            x: column2X,
            y: analysisY,
            size: textConfig.fontSize.header,
            color: themeColors.primary
          });
          analysisY -= textConfig.spacing.section;

          // Column 3: Recommendations content
          let recommendationsY = contentStartY;
          newPage.drawText('Recommendations', {
            x: column3X,
            y: recommendationsY,
            size: textConfig.fontSize.header,
            color: themeColors.primary
          });
          recommendationsY -= textConfig.spacing.section;

          questionsForThisPage.forEach((question: QuestionBreakdown) => {
            if (question.structuredFeedback && question.structuredFeedback.length > 0) {
              const pageFeedback = question.structuredFeedback
                .map(section => ({
                  ...section,
                  subsections: section.subsections?.filter(subsection =>
                    subsection.bounding_box && subsection.bounding_box.page === pageNumber
                  ) || []
                }))
                .filter(section => section.subsections.length > 0);

              pageFeedback.forEach(section => {
                section.subsections?.forEach(subsection => {
                  if (subsection.content) {
                    const structured = extractStructuredContent(subsection.content);

                    // Add paragraph title if available
                    if (subsection.title) {
                      analysisY = drawText(
                        `• ${subsection.title}`,
                        column2X,
                        analysisY,
                        columnWidth,
                        textConfig.fontSize.body,
                        newPage,
                        themeColors.primary,
                        { isBold: true }
                      );
                    }

                    // Column 2: Analysis content (Core Claim, Evidence, Reasoning, Analysis)
                    if (structured.coreClaim) {
                      analysisY = drawText(
                        `Core Claim: ${structured.coreClaim}`,
                        column2X + 5,
                        analysisY,
                        columnWidth - 5,
                        textConfig.fontSize.small,
                        newPage,
                        themeColors.muted
                      );
                    }

                    if (structured.evidence) {
                      analysisY = drawText(
                        `Evidence: ${structured.evidence}`,
                        column2X + 5,
                        analysisY,
                        columnWidth - 5,
                        textConfig.fontSize.small,
                        newPage,
                        themeColors.muted
                      );
                    }

                    if (structured.reasoning) {
                      analysisY = drawText(
                        `Reasoning: ${structured.reasoning}`,
                        column2X + 5,
                        analysisY,
                        columnWidth - 5,
                        textConfig.fontSize.small,
                        newPage,
                        themeColors.muted
                      );
                    }

                    if (structured.analysis) {
                      analysisY = drawText(
                        `Analysis: ${structured.analysis}`,
                        column2X + 5,
                        analysisY,
                        columnWidth - 5,
                        textConfig.fontSize.small,
                        newPage,
                        themeColors.muted
                      );
                    }

                    // Column 3: Recommendations (Specific Improvements, Evidence to Add, Structural Fix)
                    if (structured.specificImprovements) {
                      recommendationsY = drawText(
                        `Improvements: ${structured.specificImprovements}`,
                        column3X + 5,
                        recommendationsY,
                        columnWidth - 5,
                        textConfig.fontSize.small,
                        newPage,
                        themeColors.warning
                      );
                    }

                    if (structured.evidenceToAdd) {
                      recommendationsY = drawText(
                        `Evidence to Add: ${structured.evidenceToAdd}`,
                        column3X + 5,
                        recommendationsY,
                        columnWidth - 5,
                        textConfig.fontSize.small,
                        newPage,
                        themeColors.warning
                      );
                    }

                    if (structured.structuralFix) {
                      recommendationsY = drawText(
                        `Structural Fix: ${structured.structuralFix}`,
                        column3X + 5,
                        recommendationsY,
                        columnWidth - 5,
                        textConfig.fontSize.small,
                        newPage,
                        themeColors.warning
                      );
                    }

                    // Add spacing between paragraphs
                    analysisY -= textConfig.spacing.paragraph;
                    recommendationsY -= textConfig.spacing.paragraph;
                  }
                });
              });
            }
          });
        } else {
          // No feedback for this page
          drawText(
            'No specific feedback for this page',
            column2X,
            contentStartY - 50,
            columnWidth * 2,
            textConfig.fontSize.body,
            newPage,
            themeColors.lightMuted
          );
        }

        // Add compact footer
        newPage.drawText('Generated by AegisScholar - AI-Powered Educational Assessment Platform', {
          x: margin,
          y: 25,
          size: textConfig.fontSize.tiny,
          color: themeColors.muted
        });
      }

      // Save and download the side-by-side PDF
      const sideBySidePdfBytes = await newPdfDoc.save();
      const blob = new Blob([new Uint8Array(sideBySidePdfBytes)], { type: 'application/pdf' });

      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${submissionData.studentName.replace(/\s+/g, '_')}_Annotated_Answer_Sheet_${new Date().toISOString().split('T')[0]}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      console.log('Side-by-side answer sheet with feedback created and downloaded successfully');

    } catch (error) {
      console.error('Error creating side-by-side answer sheet:', error);
      throw error;
    }
  };



  return (
    <div className="download-report-container space-y-3">
      <div className="flex flex-row gap-2">
        {submissionData.pdfUrl && (
          <button
            onClick={downloadSideBySideReport}
            disabled={isDownloadingSideBySide}
            className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-foreground bg-muted hover:bg-accent/50 border border-border rounded-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Download Annotated Answer Sheet"
          >
            <ViewColumnsIcon className="w-4 h-4" />
            <span className="hidden sm:inline">
              {isDownloadingSideBySide ? 'Generating...' : 'Download Annotated Answer Sheet'}
            </span>
            <span className="sm:hidden">
              {isDownloadingSideBySide ? 'Generating...' : 'Annotated'}
            </span>
          </button>
        )}
      </div>

      {/* Development preview - remove in production */}
      {/* {process.env.NODE_ENV === 'development' && submissionData.detailedBreakdown?.questions?.length > 0 && (
        <div className="mt-4 space-y-2">
          <h3 className="text-sm font-medium text-foreground">Feedback Preview (Development Only):</h3>
          {submissionData.detailedBreakdown.questions.slice(0, 2).map((question, index) => (
            <div key={index} className="space-y-2">
              <h4 className="text-xs font-medium text-muted-foreground">Question {question.questionNumber}:</h4>
              {question.structuredFeedback?.map((section, sectionIndex) => (
                <div key={sectionIndex}>
                  {section.subsections?.slice(0, 1).map((subsection, subIndex) => (
                    <MarkdownPreview key={subIndex} content={subsection.content || ''} />
                  ))}
                </div>
              ))}
            </div>
          ))}
        </div>
      )} */}
    </div>
  );
};

export default DownloadStudentReport; 